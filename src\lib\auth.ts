import { SignJWT, jwtVerify } from 'jose';
import { NextRequest } from 'next/server';

// Use Web Crypto API for Edge compatibility
const JWT_SECRET = new TextEncoder().encode(
  process.env.JWT_SECRET || 'your-secret-key'
);

const alg = 'HS256';

export async function generateToken(username: string): Promise<string> {
  try {
    const token = await new SignJWT({ username })
      .setProtectedHeader({ alg })
      .setExpirationTime('24h')
      .setIssuedAt()
      .sign(JWT_SECRET);
    return token;
  } catch (error) {
    console.error('Token generation error:', error);
    throw error;
  }
}

export async function verifyToken(token: string): Promise<boolean> {
  if (!token) return false;

  try {
    const { payload } = await jwtVerify(token, JWT_SECRET);
    return typeof payload.username === 'string';
  } catch (error) {
    console.error('Token verification error:', error);
    return false;
  }
}

export async function verifyAuth(username: string, password: string): Promise<boolean> {
  if (!process.env.ADMIN_USERNAME || !process.env.ADMIN_PASSWORD) {
    console.error('Admin credentials not configured in .env');
    return false;
  }
  
  const isValid = username === process.env.ADMIN_USERNAME && 
                 password === process.env.ADMIN_PASSWORD;
  
  if (!isValid) {
    console.log('Invalid credentials provided for username:', username);
  }
  
  return isValid;
}
