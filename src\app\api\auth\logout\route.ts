import { NextResponse } from "next/server";

export async function POST() {
  try {
    const response = NextResponse.json({ success: true });
    
    // Clear the auth token
    response.cookies.set({
      name: "admin_token",
      value: "",
      expires: new Date(0),
      path: "/"
    });

    console.log("Logout successful");
    return response;
  } catch (error) {
    console.error("Logout error:", error);
    return NextResponse.json(
      { error: "Logout failed" },
      { status: 500 }
    );
  }
}
