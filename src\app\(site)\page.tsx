import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";
import {
  ArrowRight,
  Download,
  Github,
  Linkedin,
  User,
  Lightbulb,
  Briefcase,
  Mail,
  Code,
  FileText,
  type LucideIcon,
  Menu,
  X,
  <PERSON>,
  <PERSON>,
  <PERSON>pt<PERSON>,
} from "lucide-react";
import Image from "next/image";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
} from "@/components/ui/card";
import { ProjectCard } from "@/components/shared/ProjectCard";
import { getProjects } from "@/actions/projectActions";
import { getSkills } from "@/actions/skillActions";
import { getAboutData } from "@/actions/aboutActions";
import { getExperience } from "@/actions/experienceActions";
import { getCurrentCvInfo } from "@/actions/cvActions";
import * as LucideIcons from "lucide-react";

// Helper component for section titles with enhanced styling
const SectionTitle = ({
  title,
  icon: Icon,
}: {
  title: string;
  icon?: LucideIcon;
}) => (
  <div className="flex items-center justify-center mb-16 relative group">
    <div className="absolute inset-0 bg-gradient-to-r from-transparent via-primary/20 to-transparent h-px top-1/2 transform -translate-y-1/2 scale-x-0 group-hover:scale-x-100 transition-transform duration-700"></div>
    {Icon && (
      <div className="relative mr-4 p-3 bg-gradient-to-br from-primary/10 to-accent/10 rounded-2xl border border-primary/20 backdrop-blur-sm group-hover:scale-110 transition-all duration-300">
        <Icon className="h-8 w-8 text-primary" />
      </div>
    )}
    <h2 className="text-3xl sm:text-4xl lg:text-5xl font-black text-center relative z-10">
      <span className="text-transparent bg-clip-text bg-gradient-to-r from-primary via-accent to-primary bg-size-200 animate-gradient">
        {title}
      </span>
    </h2>
  </div>
);

export default async function HomePage() {
  const projects = await getProjects();
  const featuredProjects = projects.slice(0, 3);

  const skills = await getSkills();
  const keySkills = skills.slice(0, 6);

  const aboutData = await getAboutData();
  const experience = await getExperience();
  const recentExperience = experience.length > 0 ? experience[0] : null;

  const cvInfo = await getCurrentCvInfo();
  const isLocalProfilePic = aboutData.profilePictureUrl?.startsWith("/");

  return (
    <div className="min-h-screen bg-background text-foreground flex flex-col antialiased overflow-x-hidden">
      {/* Hero Section with Enhanced Design */}
      <section
        id="home"
        className="min-h-screen flex items-center justify-center relative overflow-hidden"
      >
        {/* Animated Background */}
        <div className="absolute inset-0 bg-gradient-to-br from-background via-primary/5 to-accent/10">
          <div className="absolute inset-0 bg-[radial-gradient(circle_at_20%_80%,rgba(120,119,198,0.1),transparent_50%)]"></div>
          <div className="absolute inset-0 bg-[radial-gradient(circle_at_80%_20%,rgba(255,119,198,0.1),transparent_50%)]"></div>
          <div className="absolute inset-0 bg-[radial-gradient(circle_at_40%_40%,rgba(120,219,255,0.05),transparent_50%)]"></div>
        </div>

        {/* Floating Elements */}
        <div className="absolute inset-0 overflow-hidden pointer-events-none">
          <div className="absolute top-1/4 left-1/4 w-2 h-2 bg-primary/30 rounded-full animate-float"></div>
          <div className="absolute top-3/4 right-1/4 w-3 h-3 bg-accent/30 rounded-full animate-float-delayed"></div>
          <div className="absolute bottom-1/4 left-1/3 w-1 h-1 bg-primary/40 rounded-full animate-pulse"></div>
        </div>

        <div className="container mx-auto max-w-7xl text-center z-10 px-4 sm:px-6 lg:px-8">
          <div className="grid lg:grid-cols-2 gap-12 lg:gap-16 items-center">
            {/* Text Content */}
            <div className="lg:text-left space-y-8 order-2 lg:order-1">
              <div className="space-y-4">
                <div className="inline-flex items-center px-4 py-2 bg-primary/10 border border-primary/20 rounded-full backdrop-blur-sm">
                  <span className="text-2xl font-semibold uppercase tracking-wider text-primary">
                    👋 Hi, I'm {aboutData.fullName || "Your Name"}
                  </span>
                </div>

                <h1 className="text-4xl sm:text-5xl lg:text-6xl xl:text-7xl font-black tracking-tight leading-none">
                  <span className="block mb-2">Creative</span>
                  <span className="block text-transparent bg-clip-text bg-gradient-to-r from-primary to-accent mb-2">
                    Developer
                  </span>
                  <span className="block text-2xl sm:text-3xl lg:text-4xl xl:text-5xl text-muted-foreground font-light">
                    & UI/UX Enthusiast
                  </span>
                </h1>
              </div>

              <p className="text-lg sm:text-xl text-muted-foreground max-w-2xl lg:mx-0 mx-auto leading-relaxed">
                {aboutData.bioParagraphs?.[0] ||
                  "I craft beautiful, engaging, and accessible digital experiences. Passionate about clean code, intuitive design, and pushing creative boundaries to build solutions that matter."}
              </p>

              {/* CTA Buttons */}
              <div className="flex flex-col sm:flex-row lg:justify-start justify-center gap-4">
                <Button
                  size="lg"
                  asChild
                  className="group bg-gradient-to-r from-primary to-accent hover:from-primary/90 hover:to-accent/90 text-primary-foreground shadow-sm hover:shadow-2xl transition-all duration-300 transform hover:scale-105 px-8 py-4 text-lg font-semibold rounded-xl"
                >
                  <Link href="#contact">
                    Contact Me
                    <ArrowRight className="ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform duration-300" />
                  </Link>
                </Button>

                <Button
                  size="lg"
                  variant="outline"
                  asChild
                  className="group border-2 border-primary/30 text-primary hover:bg-primary/10 hover:border-primary shadow-sm hover:shadow-2xl transition-all duration-300 transform hover:scale-105 px-8 py-4 text-lg font-semibold rounded-xl backdrop-blur-sm"
                >
                  <Link href="#projects">
                    View My Work
                    <ArrowRight className="ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform duration-300" />
                  </Link>
                </Button>

                {cvInfo?.fileName && (
                  <Button
                    size="lg"
                    variant="ghost"
                    asChild
                    className="group text-primary hover:bg-primary/10 shadow-sm hover:shadow-xl transition-all duration-300 transform hover:scale-105 px-8 py-4 text-lg font-semibold rounded-xl border border-primary/20"
                  >
                    <Link
                      href={`/${cvInfo.fileName}`}
                      download={`${(aboutData.fullName || "User").replace(
                        /\s+/g,
                        "_"
                      )}_CV.pdf`}
                      target="_blank"
                      rel="noopener noreferrer"
                    >
                      <Download className="mr-2 h-5 w-5 group-hover:animate-bounce" />
                      Download CV
                    </Link>
                  </Button>
                )}
              </div>

              {/* Social Links */}
              <div className="flex lg:justify-start justify-center space-x-6 items-center pt-4">
                {aboutData.socialLinks?.slice(0, 3).map((link) => {
                  const IconComponent =
                    link.iconName &&
                    LucideIcons[link.iconName as keyof typeof LucideIcons]
                      ? (LucideIcons[
                          link.iconName as keyof typeof LucideIcons
                        ] as LucideIcon)
                      : Github;
                  return (
                    <Link
                      key={link.id}
                      href={link.url}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="group p-3 bg-card/50 backdrop-blur-sm border border-border/50 rounded-xl text-muted-foreground hover:text-primary hover:border-primary/50 transition-all duration-300 transform hover:scale-110 hover:-translate-y-1"
                    >
                      <IconComponent className="h-6 w-6" />
                    </Link>
                  );
                })}
              </div>
            </div>

            {/* Profile Image */}
            <div className="relative group flex justify-center items-center order-1 lg:order-2">
              <div className="absolute -inset-4 bg-gradient-to-r from-primary via-accent to-primary rounded-full blur-2xl opacity-30 group-hover:opacity-50 transition-all duration-1000 animate-spin-slow"></div>
              <div className="absolute -inset-2 bg-gradient-to-r from-accent to-primary rounded-full blur-xl opacity-20 group-hover:opacity-40 transition-all duration-700 animate-pulse"></div>
              <div className="relative">
                <Image
                  src={
                    aboutData.profilePictureUrl ||
                    "https://placehold.co/600x600/333/fff.png?text=Me"
                  }
                  alt={`Profile of ${aboutData.fullName || "User"}`}
                  width={400}
                  height={400}
                  className="rounded-full shadow-sm object-cover aspect-square relative z-10 border-4 border-background transform group-hover:scale-105 transition-all duration-500 w-64 h-64 sm:w-80 sm:h-80 lg:w-96 lg:h-96"
                  priority
                  unoptimized={
                    !isLocalProfilePic &&
                    aboutData.profilePictureUrl?.includes("placehold.co")
                  }
                />
                <div className="absolute inset-0 rounded-full bg-gradient-to-tr from-primary/20 to-accent/20 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* About Me Section */}
      <section
        id="about"
        className="py-20  bg-gradient-to-b from-muted/30 to-background relative"
      >
        <div className="absolute inset-0 bg-[linear-gradient(45deg,transparent_25%,rgba(120,119,198,0.02)_50%,transparent_75%)]"></div>
        <div className="container mx-auto max-w-6xl px-4 sm:px-6 lg:px-8 relative z-10">
          <SectionTitle title="About Me" icon={User} />

          <Card className="group shadow-sm overflow-hidden bg-card/80 backdrop-blur-xl border border-border/50 hover:border-primary/30 transition-all duration-700 transform hover:-translate-y-4 rounded-3xl">
            <div className="absolute inset-0 bg-gradient-to-br from-primary/5 via-transparent to-accent/5 opacity-0 group-hover:opacity-100 transition-opacity duration-700"></div>

            <CardHeader className="p-8 lg:p-12 relative z-10">
              <div className="flex items-start space-x-4">
                <div className="p-4 bg-gradient-to-br from-primary/10 to-accent/10 rounded-2xl border border-primary/20">
                  <User className="h-8 w-8 text-primary" />
                </div>
                <div className="flex-1">
                  <CardTitle className="text-2xl lg:text-4xl font-bold text-foreground leading-tight mb-3">
                    A Little More About My Journey
                  </CardTitle>
                  <CardDescription className="text-lg lg:text-xl text-muted-foreground">
                    Developer, Designer, Dreamer.
                  </CardDescription>
                </div>
              </div>
            </CardHeader>

            <CardContent className="p-8 lg:p-12 pt-0 relative z-10">
              <div className="space-y-6 text-base lg:text-lg text-muted-foreground leading-relaxed">
                <p className="first-letter:text-4xl first-letter:font-bold first-letter:text-primary first-letter:mr-2 first-letter:float-left first-letter:leading-none">
                  {aboutData.bioParagraphs?.[1] ||
                    "I'm a dedicated developer passionate about crafting impactful digital solutions and exploring new technologies. I thrive in collaborative environments, turning complex challenges into elegant, user-friendly experiences."}
                </p>
                <p>
                  {aboutData.bioParagraphs?.[2] ||
                    "My approach combines technical expertise with a keen eye for design, ensuring that every project is not only functional but also aesthetically pleasing and intuitive to use. I'm always eager to learn and grow, embracing new challenges as opportunities to expand my skillset."}
                </p>
              </div>

              <div className="pt-8">
                <Button
                  asChild
                  variant="ghost"
                  className="group text-primary hover:bg-primary/10 hover:text-primary font-semibold text-lg px-6 py-3 rounded-xl transition-all duration-300"
                >
                  <Link href="/about">
                    Discover Full Story
                    <ArrowRight className="ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform duration-300" />
                  </Link>
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </section>

      {/* Skills Section */}
      {keySkills.length > 0 && (
        <section
          id="skills"
          className="py-20  bg-background relative overflow-hidden"
        >
          <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_center,rgba(120,119,198,0.05),transparent_70%)]"></div>

          <div className="container mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 relative z-10">
            <SectionTitle title="My Tech Arsenal" icon={Lightbulb} />

            <p className="text-lg lg:text-xl text-center text-muted-foreground mb-16 max-w-4xl mx-auto leading-relaxed">
              I'm proficient in a range of modern web technologies, always eager
              to learn and adapt. Here are some of the tools and technologies I
              love working with:
            </p>

            <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-6 gap-6 lg:gap-8 mb-16">
              {keySkills.map((skill, index) => {
                const IconComponent =
                  skill.iconName &&
                  LucideIcons[skill.iconName as keyof typeof LucideIcons]
                    ? (LucideIcons[
                        skill.iconName as keyof typeof LucideIcons
                      ] as LucideIcon)
                    : Code;
                return (
                  <Card
                    key={skill.id}
                    className="group p-6 lg:p-8 bg-card/60 backdrop-blur-sm border border-border/50 hover:border-primary/50 shadow-sm hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2 hover:scale-105 flex flex-col items-center justify-center text-center aspect-square rounded-2xl relative overflow-hidden"
                    style={{
                      animationDelay: `${index * 100}ms`,
                    }}
                  >
                    <div className="absolute inset-0 bg-gradient-to-br from-primary/5 to-accent/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                    <IconComponent className="h-10 w-10 lg:h-12 lg:w-12 text-primary mb-4 transition-all duration-300 group-hover:scale-125 group-hover:rotate-6 relative z-10" />
                    <p className="font-semibold text-foreground text-sm lg:text-base relative z-10 group-hover:text-primary transition-colors duration-300">
                      {skill.name}
                    </p>
                  </Card>
                );
              })}
            </div>

            <div className="text-center">
              <Button
                asChild
                size="lg"
                className="group bg-gradient-to-r from-primary to-accent hover:from-primary/90 hover:to-accent/90 text-primary-foreground shadow-sm hover:shadow-2xl transition-all duration-300 transform hover:scale-105 px-8 py-4 text-lg font-semibold rounded-xl"
              >
                <Link href="/skills">
                  Explore All My Skills
                  <ArrowRight className="ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform duration-300" />
                </Link>
              </Button>
            </div>
          </div>
        </section>
      )}

      {/* Featured Projects Section */}
      {featuredProjects.length > 0 && (
        <section
          id="projects"
          className="py-20  bg-gradient-to-b from-muted/30 to-background relative"
        >
          <div className="absolute inset-0 bg-[linear-gradient(-45deg,transparent_25%,rgba(255,119,198,0.02)_50%,transparent_75%)]"></div>

          <div className="container mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 relative z-10">
            <SectionTitle title="Featured Creations" icon={Briefcase} />

            <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-5 mb-6">
              {featuredProjects.map((project, index) => (
                <div
                  key={project.id}
                  className="transform transition-all duration-500 hover:scale-105"
                  style={{
                    animationDelay: `${index * 200}ms`,
                  }}
                >
                  <ProjectCard project={project} />
                </div>
              ))}
            </div>

            <div className="text-center">
              <Button
                asChild
                size="lg"
                variant="outline"
                className="group border-2 border-primary/30 text-primary hover:bg-primary/10 hover:border-primary shadow-sm hover:shadow-2xl transition-all duration-300 transform hover:scale-105 px-8 py-4 text-lg font-semibold rounded-xl backdrop-blur-sm"
              >
                <Link href="/projects">
                  View All Projects
                  <ArrowRight className="ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform duration-300" />
                </Link>
              </Button>
            </div>
          </div>
        </section>
      )}

      {/* Experience Highlights Section */}
      {recentExperience && (
        <section
          id="experience"
          className="py-20  bg-background relative"
        >
          <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_bottom,rgba(120,219,255,0.05),transparent_70%)]"></div>

          <div className="container mx-auto max-w-6xl px-4 sm:px-6 lg:px-8 relative z-10">
            <SectionTitle title="Professional Journey" icon={FileText} />

            <Card className="group shadow-sm overflow-hidden bg-card/80 backdrop-blur-xl border border-border/50 hover:border-accent/30 transition-all duration-700 transform hover:-translate-y-4 rounded-3xl">
              <div className="absolute inset-0 bg-gradient-to-br from-accent/5 via-transparent to-primary/5 opacity-0 group-hover:opacity-100 transition-opacity duration-700"></div>

              <CardHeader className="bg-gradient-to-r from-accent/10 to-primary/10 p-8 lg:p-12 relative z-10">
                <div className="flex items-center gap-6">
                  <div className="p-4 bg-gradient-to-br from-primary/20 to-accent/20 rounded-2xl border border-primary/30 backdrop-blur-sm">
                    <FileText className="h-8 w-8 lg:h-10 lg:w-10 text-primary flex-shrink-0" />
                  </div>
                  <div className="flex-1">
                    <CardTitle className="text-2xl lg:text-4xl font-bold text-primary leading-tight mb-2">
                      My Recent Role
                    </CardTitle>
                    <CardDescription className="text-lg lg:text-xl text-muted-foreground">
                      At{" "}
                      <span className="font-semibold text-accent">
                        {recentExperience.companyName}
                      </span>{" "}
                      as a{" "}
                      <span className="font-semibold">
                        {recentExperience.role}
                      </span>
                    </CardDescription>
                  </div>
                </div>
              </CardHeader>

              <CardContent className="p-8 lg:p-12 relative z-10">
                <p className="text-lg lg:text-xl text-muted-foreground leading-relaxed mb-8 first-letter:text-3xl first-letter:font-bold first-letter:text-primary first-letter:mr-2 first-letter:float-left">
                  {recentExperience.description?.[0]
                    ? `${
                        recentExperience.description[0]
                          .charAt(0)
                          .toUpperCase() +
                        recentExperience.description[0].slice(1)
                      }`
                    : "Engaged in challenging and rewarding projects, contributing to the company's success through innovative solutions and teamwork."}
                </p>

                <Button
                  asChild
                  variant="ghost"
                  className="group text-primary hover:bg-primary/10 hover:text-primary font-semibold text-lg px-6 py-3 rounded-xl transition-all duration-300"
                >
                  <Link href="/experience">
                    See My Full Experience
                    <ArrowRight className="ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform duration-300" />
                  </Link>
                </Button>
              </CardContent>
            </Card>
          </div>
        </section>
      )}

      {/* Contact Section */}
      <section id="contact" className="py-20  relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-primary via-accent to-primary"></div>
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_30%_40%,rgba(255,255,255,0.1),transparent_50%)]"></div>
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_70%_60%,rgba(255,255,255,0.05),transparent_50%)]"></div>

        <div className="container mx-auto max-w-4xl px-4 sm:px-6 lg:px-8 text-center relative z-10">
          <div className="mb-8">
            <div className="inline-flex p-6 bg-white/10 backdrop-blur-sm rounded-full border border-white/20 mb-8">
              <Mail className="h-12 w-12 lg:h-16 lg:w-16 text-white/90" />
            </div>

            <h2 className="text-4xl lg:text-6xl font-black mb-6 text-white">
              Let's Connect!
            </h2>

            <p className="text-lg lg:text-xl mb-12 text-white/90 max-w-3xl mx-auto leading-relaxed">
              Have a project in mind, a question, or just want to say hi? I'd
              love to hear from you. Feel free to reach out and let's create
              something amazing together!
            </p>
          </div>

          <Button
            size="lg"
            variant="secondary"
            asChild
            className="group bg-white/95 backdrop-blur-sm text-primary hover:bg-white hover:scale-105 shadow-sm hover:shadow-3xl transition-all duration-300 transform px-12 py-6 text-lg lg:text-xl font-bold rounded-2xl border-2 border-white/20"
          >
            <Link href="/contact">
              Get In Touch
              <ArrowRight className="ml-3 h-6 w-6 group-hover:translate-x-1 transition-transform duration-300" />
            </Link>
          </Button>
        </div>
      </section>
    </div>
  );
}

