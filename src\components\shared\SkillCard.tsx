
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON>Header, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import type { Skill } from '@/types';
import type { LucideIcon } from 'lucide-react';
import * as LucideIcons from 'lucide-react'; // Import all
import { Lightbulb } from 'lucide-react'; // Default icon

interface SkillCardProps {
  skill: Skill;
}

export function SkillCard({ skill }: SkillCardProps) {
  const IconComponent = (skill.iconName && LucideIcons[skill.iconName as keyof typeof LucideIcons])
    ? LucideIcons[skill.iconName as keyof typeof LucideIcons] as LucideIcon
    : Lightbulb;

  const proficiencyValue = (level?: Skill['proficiencyLevel']) => {
    switch (level) {
      case 'Beginner': return 25;
      case 'Intermediate': return 50;
      case 'Advanced': return 75;
      case 'Expert': return 100;
      default: return 0;
    }
  };

  return (
    <Card className="shadow-lg hover:shadow-xl transition-shadow duration-300 ease-in-out">
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-lg font-medium text-foreground">{skill.name}</CardTitle>
        {IconComponent && <IconComponent className="h-6 w-6 text-primary" />}
      </CardHeader>
      <CardContent>
        {skill.proficiencyLevel && proficiencyValue(skill.proficiencyLevel) > 0 && (
          <div className="mt-2">
            <div className="flex justify-between text-xs text-muted-foreground mb-1">
              <span>Proficiency</span>
              <span>{skill.proficiencyLevel}</span>
            </div>
            <Progress value={proficiencyValue(skill.proficiencyLevel)} aria-label={`${skill.name} proficiency: ${skill.proficiencyLevel}`} className="h-2" />
          </div>
        )}
        <p className="text-xs text-muted-foreground mt-3">{skill.category}</p>
      </CardContent>
    </Card>
  );
}
