
"use client";
import { useState, useEffect, type FormEvent } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { useToast } from "@/hooks/use-toast";
import { getProjectById, updateProject } from '@/actions/projectActions';
import Link from 'next/link';
import NextImage from 'next/image'; // Renamed to avoid conflict
import { ArrowLeft, Save, Image as ImageIcon } from 'lucide-react';
import type { Project } from '@/types';
import type { ZodIssue } from 'zod';

export default function EditProjectPage() {
  const router = useRouter();
  const params = useParams();
  const projectId = params.id as string;
  const { toast } = useToast();

  const [project, setProject] = useState<Project | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errors, setErrors] = useState<Record<string, string | undefined>>({});
  const [imagePreview, setImagePreview] = useState<string | null>(null);

  useEffect(() => {
    if (projectId) {
      const fetchProject = async () => {
        setIsLoading(true);
        const fetchedProject = await getProjectById(projectId);
        if (fetchedProject) {
          setProject(fetchedProject);
          if (fetchedProject.imageUrl) {
            setImagePreview(fetchedProject.imageUrl); // Set initial preview to current image
          }
        } else {
          toast({ title: "Error", description: "Project not found.", variant: "destructive" });
          router.push('/admin/projects');
        }
        setIsLoading(false);
      };
      fetchProject();
    }
  }, [projectId, router, toast]);

  const handleImageChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onloadend = () => {
        setImagePreview(reader.result as string);
      };
      reader.readAsDataURL(file);
       setErrors(prev => ({ ...prev, imageFile: undefined }));
    } else {
      // If file is deselected, revert to original project image URL for preview if available
      setImagePreview(project?.imageUrl || null);
    }
  };

  const handleSubmit = async (event: FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    if (!project) return;

    setIsSubmitting(true);
    setErrors({});
    const formData = new FormData(event.currentTarget);
    formData.append('currentImageUrl', project.imageUrl || ''); // Pass current image URL for backend logic
    
    const result = await updateProject(project.id, formData);

    if (result.success) {
      toast({
        title: "Project Updated",
        description: result.message,
      });
      router.push('/admin/projects');
    } else {
      toast({
        title: "Error Updating Project",
        description: result.message || "An unknown error occurred.",
        variant: "destructive",
      });
      if (result.errors) {
        const newErrors: Record<string, string> = {};
         if (Array.isArray(result.errors)) { // ZodIssue[]
            result.errors.forEach((err: ZodIssue) => {
            if (err.path[0]) {
                newErrors[err.path[0] as string] = err.message;
            }
            });
        } else { // Custom error like { imageFile: 'message' }
            Object.assign(newErrors, result.errors);
        }
        setErrors(newErrors);
      }
    }
    setIsSubmitting(false);
  };

  if (isLoading) return <p>Loading project data...</p>;
  if (!project) return <p>Project not found.</p>;

  return (
    <div className="space-y-6">
      <Button variant="outline" size="sm" asChild className="mb-4">
        <Link href="/admin/projects"><ArrowLeft className="mr-2 h-4 w-4" />Back to Projects</Link>
      </Button>
      <Card>
        <CardHeader>
          <CardTitle>Edit Project: {project.title}</CardTitle>
          <CardDescription>Update the project details below.</CardDescription>
        </CardHeader>
        <form onSubmit={handleSubmit}>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="title">Title</Label>
              <Input id="title" name="title" defaultValue={project.title} required disabled={isSubmitting}/>
              {errors.title && <p className="text-sm text-destructive mt-1">{errors.title}</p>}
            </div>
            <div>
              <Label htmlFor="shortDescription">Short Description</Label>
              <Textarea id="shortDescription" name="shortDescription" defaultValue={project.shortDescription} required disabled={isSubmitting}/>
              {errors.shortDescription && <p className="text-sm text-destructive mt-1">{errors.shortDescription}</p>}
            </div>
            <div>
              <Label htmlFor="technologies">Technologies (comma-separated)</Label>
              <Input id="technologies" name="technologies" defaultValue={project.technologies.join(', ')} placeholder="Next.js, TypeScript" required disabled={isSubmitting}/>
              {errors.technologies && <p className="text-sm text-destructive mt-1">{errors.technologies}</p>}
            </div>
            <div>
              <Label htmlFor="imageFile">Project Image (leave blank to keep current)</Label>
              <Input 
                id="imageFile" 
                name="imageFile" 
                type="file" 
                accept="image/png, image/jpeg, image/webp" 
                disabled={isSubmitting}
                onChange={handleImageChange}
                className="file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-primary/10 file:text-primary hover:file:bg-primary/20"
              />
              {imagePreview && (
                <div className="mt-2">
                  <p className="text-xs text-muted-foreground mb-1">Current/New image preview:</p>
                  {/* eslint-disable-next-line @next/next/no-img-element */}
                  <img src={imagePreview} alt="Project image preview" className="h-32 w-auto rounded-md object-cover border" />
                </div>
              )}
              {errors.imageFile && <p className="text-sm text-destructive mt-1">{errors.imageFile}</p>}
            </div>
            <div>
              <Label htmlFor="liveDemoUrl">Live Demo URL (Optional)</Label>
              <Input id="liveDemoUrl" name="liveDemoUrl" type="url" defaultValue={project.liveDemoUrl || ''} disabled={isSubmitting}/>
              {errors.liveDemoUrl && <p className="text-sm text-destructive mt-1">{errors.liveDemoUrl}</p>}
            </div>
            <div>
              <Label htmlFor="githubRepoUrl">GitHub Repository URL (Optional)</Label>
              <Input id="githubRepoUrl" name="githubRepoUrl" type="url" defaultValue={project.githubRepoUrl || ''} disabled={isSubmitting}/>
              {errors.githubRepoUrl && <p className="text-sm text-destructive mt-1">{errors.githubRepoUrl}</p>}
            </div>
            <div>
              <Label htmlFor="fullDescription">Full Description (HTML allowed)</Label>
              <Textarea 
                id="fullDescription" 
                name="fullDescription" 
                defaultValue={project.fullDescription || ''} 
                rows={8} 
                disabled={isSubmitting}
                className="min-h-[150px]"
                placeholder="<p>Detailed description of your project...</p><ul><li>Feature 1</li><li>Feature 2</li></ul>" 
              />
               <p className="text-xs text-muted-foreground mt-1">You can use HTML tags like &lt;p&gt;, &lt;ul&gt;, &lt;li&gt;, &lt;strong&gt; for formatting.</p>
              {errors.fullDescription && <p className="text-sm text-destructive mt-1">{errors.fullDescription}</p>}
            </div>
          </CardContent>
          <CardFooter>
            <Button type="submit" disabled={isSubmitting || isLoading}>
              <Save className="mr-2 h-4 w-4" />
              {isSubmitting ? 'Saving Changes...' : 'Save Changes'}
            </Button>
          </CardFooter>
        </form>
      </Card>
    </div>
  );
}

