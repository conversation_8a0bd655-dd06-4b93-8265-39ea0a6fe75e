import { NextResponse } from "next/server";
import { verifyAuth, generateToken } from "@/lib/auth";

export async function POST(request: Request) {
  try {
    if (!process.env.ADMIN_USERNAME || !process.env.ADMIN_PASSWORD) {
      console.error("Admin credentials not configured in .env");
      return NextResponse.json(
        { error: "Server configuration error" },
        { status: 500 }
      );
    }

    const body = await request.json();
    const { username, password } = body;

    if (!username || !password) {
      return NextResponse.json(
        { error: "Username and password are required" },
        { status: 400 }
      );
    }

    // Verify credentials
    const isValid = await verifyAuth(username, password);
    if (!isValid) {
      console.log("Invalid login attempt for username:", username);
      return NextResponse.json(
        { error: "Invalid credentials" },
        { status: 401 }
      );
    }

    try {
      // Generate JWT token
      const token = await generateToken(username);

      // Create response with cookie
      const response = NextResponse.json(
        { success: true },
        { status: 200 }
      );

      // Set cookie with token
      response.cookies.set({
        name: "admin_token",
        value: token,
        httpOnly: true,
        secure: process.env.NODE_ENV === "production",
        sameSite: "lax",
        path: "/",
        maxAge: 60 * 60 * 24, // 24 hours
      });

      console.log("Login successful for username:", username);
      return response;
    } catch (error) {
      console.error("Token generation error:", error);
      return NextResponse.json(
        { error: "Authentication failed" },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error("Login error:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
