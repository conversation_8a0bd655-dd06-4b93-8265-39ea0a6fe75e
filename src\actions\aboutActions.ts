
'use server';

import { z } from 'zod';
import { revalidatePath } from 'next/cache';
import type { EducationItem, SocialLink } from '@/types';
import { v4 as uuidv4 } from 'uuid';
import { getMongoDb } from '@/lib/mongodb';
import { ObjectId } from 'mongodb';
import path from 'path';
import fs from 'fs/promises';
import { <PERSON><PERSON><PERSON> } from 'buffer';
import { AboutData, AboutDataSchema, EducationItemSchema, SocialLinkSchema } from '@/types';

const ABOUT_COLLECTION = 'abouts';
const UPLOAD_DIR_ABOUT = path.join(process.cwd(), 'public', 'uploads', 'about');
// Use a valid 24-character hex string for the fixed ID of the singleton 'about' document.
const MAIN_ABOUT_ID = '000000000000000000000001';

const defaultAboutData: Omit<AboutData, 'id'> = {
  fullName: "Your Name",
  profilePictureUrl: 'https://placehold.co/400x500.png', // Default placeholder
  contactEmail: "<EMAIL>",
  bioParagraphs: ["Enter your bio here."],
  personalValues: ["Value 1", "Value 2"],
  origin: { city: "Your City", country: "Your Country" },
  educationHistory: [],
  socialLinks: [],
};

// Ensure upload directory exists
async function ensureUploadDirExists(dirPath: string) {
  try {
    await fs.access(dirPath);
  } catch (error) {
    await fs.mkdir(dirPath, { recursive: true });
  }
}

async function getAboutCollection() {
  const db = await getMongoDb();
  return db.collection<Omit<AboutData, 'id' | '_id'>>(ABOUT_COLLECTION);
}

export async function getAboutData(): Promise<AboutData> {
  try {
    const collection = await getAboutCollection();
    const aboutDoc = await collection.findOne({ _id: new ObjectId(MAIN_ABOUT_ID) });

    if (aboutDoc) {
      const {_id, ...rest} = aboutDoc;
      return {id: _id.toHexString(), ...rest} as AboutData;
    } else {
      const newAboutDataWithId = {
        _id: new ObjectId(MAIN_ABOUT_ID),
        ...defaultAboutData
      };
      await collection.insertOne(newAboutDataWithId as any);
      return {id: newAboutDataWithId._id.toHexString(), ...defaultAboutData};
    }
  } catch (error) {
    console.error("Error fetching or creating about data from MongoDB:", error);
    return { id: MAIN_ABOUT_ID, ...defaultAboutData };
  }
}

const MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB
const ACCEPTED_IMAGE_TYPES = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];

export async function updateAboutData(
  formData: FormData
): Promise<{ success: boolean; message: string; data?: AboutData; errors?: z.ZodIssue[] | Record<string, string> }> {

  await ensureUploadDirExists(UPLOAD_DIR_ABOUT);

  const profilePictureFile = formData.get('profilePictureFile') as File | null;
  let newProfilePicturePath: string | null = null;
  let oldProfilePicturePathToDelete: string | null = null;

  const currentAboutData = await getAboutData(); // Fetch current data to get existing URL
  let profilePictureUrlToSave = currentAboutData.profilePictureUrl;


  if (profilePictureFile && profilePictureFile.size > 0) {
    if (profilePictureFile.size > MAX_FILE_SIZE) {
      return { success: false, message: 'Validation failed.', errors: { profilePictureFile: `Image size must be less than ${MAX_FILE_SIZE / (1024 * 1024)}MB.` } };
    }
    if (!ACCEPTED_IMAGE_TYPES.includes(profilePictureFile.type)) {
      return { success: false, message: 'Validation failed.', errors: { profilePictureFile: 'Invalid image format. Only JPEG, PNG, WebP allowed.' } };
    }

    try {
      const bytes = await profilePictureFile.arrayBuffer();
      const buffer = new Uint8Array(bytes);
      const extension = path.extname(profilePictureFile.name);
      const filename = `profile-image-${Date.now()}${extension}`;
      const filepath = path.join(UPLOAD_DIR_ABOUT, filename);
      await fs.writeFile(filepath, buffer);
      newProfilePicturePath = `/uploads/about/${filename}`; // Relative path for web access

      // If current URL is a local path and different from new one, mark old for deletion
      if (currentAboutData.profilePictureUrl && currentAboutData.profilePictureUrl.startsWith('/uploads/about/') && currentAboutData.profilePictureUrl !== newProfilePicturePath) {
        oldProfilePicturePathToDelete = currentAboutData.profilePictureUrl;
      }
      profilePictureUrlToSave = newProfilePicturePath;

    } catch (error) {
      console.error('Error saving profile picture:', error);
      return { success: false, message: 'Failed to save profile picture.' };
    }
  }


  const rawDataToValidate = {
    fullName: formData.get('fullName') as string,
    profilePictureUrl: profilePictureUrlToSave, // Use the new path or existing one
    contactEmail: formData.get('contactEmail') as string,
    bioParagraphs: (formData.get('bioParagraphs') as string || '').split('\n').map(p => p.trim()).filter(p => p),
    personalValues: (formData.get('personalValues') as string || '').split(',').map(v => v.trim()).filter(v => v),
    origin: {
      city: formData.get('originCity') as string,
      country: formData.get('originCountry') as string,
    },
    educationHistory: JSON.parse(formData.get('educationHistoryJson') as string || '[]') as EducationItem[],
    socialLinks: JSON.parse(formData.get('socialLinksJson') as string || '[]') as SocialLink[],
  };

  const validationResult = AboutDataSchema.safeParse(rawDataToValidate);

  if (!validationResult.success) {
    // If validation fails after new image upload, delete the newly uploaded image to prevent orphans
    if (newProfilePicturePath) {
        try { await fs.unlink(path.join(process.cwd(), 'public', newProfilePicturePath)); }
        catch (e) { console.error("Failed to delete orphaned new profile image during validation error:", e); }
    }
    return { success: false, message: 'Validation failed. Check console for details.', errors: validationResult.error.issues };
  }

  const validatedData = validationResult.data;

  try {
    const collection = await getAboutCollection();
    const result = await collection.updateOne(
      { _id: new ObjectId(MAIN_ABOUT_ID) },
      { $set: validatedData as Partial<AboutData> }, // Cast to Partial<AboutData> to relax type checking for $set
      { upsert: true }
    );

    if (result.modifiedCount === 0 && result.upsertedCount === 0 && result.matchedCount === 0) {
         console.warn("About data update: No document was modified, upserted, or matched. This might happen if the data submitted is identical to the existing data or an issue with the query.");
    }

    // If update was successful and an old image was marked for deletion, delete it now
    if (oldProfilePicturePathToDelete) {
        try {
            await fs.unlink(path.join(process.cwd(), 'public', oldProfilePicturePathToDelete));
        } catch (e) {
            console.error("Failed to delete old profile picture after update:", e);
        }
    }

    revalidatePath('/about');
    revalidatePath('/admin/about');
    revalidatePath('/');
    revalidatePath('/contact');

    const updatedDoc = await collection.findOne({ _id: new ObjectId(MAIN_ABOUT_ID) });
    if (!updatedDoc) {
        return { success: false, message: 'Failed to retrieve updated about data.' };
    }
    const {_id, ...restData} = updatedDoc;

    return { success: true, message: 'About information updated successfully.', data: { id: _id.toHexString(), ...restData} as AboutData };
  } catch (error) {
    console.error("Error updating about data in MongoDB:", error);
    // If DB update fails after new image upload, delete the newly uploaded image
    if (newProfilePicturePath) {
        try { await fs.unlink(path.join(process.cwd(), 'public', newProfilePicturePath)); }
        catch (e) { console.error("Failed to delete orphaned new profile image after DB error:", e); }
    }
    return { success: false, message: 'Failed to update about information.' };
  }
}
