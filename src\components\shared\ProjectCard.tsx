
import Image from 'next/image';
import Link from 'next/link';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>er, Card<PERSON><PERSON>le, CardDescription } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ExternalLink, Gith<PERSON>, Eye } from 'lucide-react';
import type { Project } from '@/types';

interface ProjectCardProps {
  project: Project;
}

export function ProjectCard({ project }: ProjectCardProps) {
  // Ensure imageUrl is a valid path starting with / if it's from public folder
  const imageUrl = project.imageUrl.startsWith('/') ? project.imageUrl : `/${project.imageUrl}`;
  const imageAlt = `Screenshot of ${project.title}`;

  return (
    <Card className="flex flex-col overflow-hidden shadow-lg hover:shadow-xl transition-shadow duration-300 ease-in-out h-full">
      <CardHeader className="p-0">
        <Link href={`/projects/${project.id}`} aria-label={`View details for ${project.title}`}>
          <div className="aspect-video relative overflow-hidden">
            <Image
              src={imageUrl} // Using the path directly
              alt={imageAlt}
              fill
              style={{ objectFit: "cover" }}
              className="hover:scale-105 transition-transform duration-300"
              data-ai-hint="project interface" // Will be replaced by actual image content
            />
          </div>
        </Link>
      </CardHeader>
      <CardContent className="flex-grow p-6 space-y-3">
        <CardTitle className="text-xl font-semibold text-foreground hover:text-primary">
          <Link href={`/projects/${project.id}`}>{project.title}</Link>
        </CardTitle>
        <CardDescription className="text-sm text-muted-foreground line-clamp-3">
          {project.shortDescription}
        </CardDescription>
        <div className="flex flex-wrap gap-2 pt-2">
          {project.technologies.slice(0, 4).map((tech, index) => (
            <Badge key={`${project.id}-tech-${index}-${tech}`} variant="secondary" className="text-xs">
              {tech}
            </Badge>
          ))}
          {project.technologies.length > 4 && (
            <Badge key={`${project.id}-more-tech`} variant="outline" className="text-xs">
              +{project.technologies.length - 4} more
            </Badge>
          )}
        </div>
      </CardContent>
      <CardFooter className="p-6 pt-0 flex flex-col sm:flex-row justify-between items-center gap-3">
        <Button variant="outline" size="sm" asChild className="w-full sm:w-auto">
            <Link href={`/projects/${project.id}`}>
                <Eye className="mr-2 h-4 w-4" /> View Details
            </Link>
        </Button>
        <div className="flex gap-2 w-full sm:w-auto">
            {project.liveDemoUrl && (
            <Button variant="ghost" size="icon" asChild title="Live Demo">
                <Link href={project.liveDemoUrl} target="_blank" rel="noopener noreferrer">
                <ExternalLink className="h-5 w-5 text-muted-foreground hover:text-primary" />
                </Link>
            </Button>
            )}
            {project.githubRepoUrl && (
            <Button variant="ghost" size="icon" asChild title="GitHub Repository">
                <Link href={project.githubRepoUrl} target="_blank" rel="noopener noreferrer">
                <Github className="h-5 w-5 text-muted-foreground hover:text-primary" />
                </Link>
            </Button>
            )}
        </div>
      </CardFooter>
    </Card>
  );
}

