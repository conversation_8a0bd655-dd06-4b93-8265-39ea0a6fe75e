
'use server';

import { z } from 'zod';
import { revalidatePath } from 'next/cache';
import type { Experience } from '@/types';
import { getMongoDb, fromMongoDocument } from '@/lib/mongodb';
import { ObjectId, type Collection } from 'mongodb';

const EXPERIENCE_COLLECTION = 'experiences';

async function getExperienceCollection(): Promise<Collection<Omit<Experience, 'id'>>> {
  const db = await getMongoDb();
  return db.collection<Omit<Experience, 'id'>>(EXPERIENCE_COLLECTION);
}

export async function getExperience(): Promise<Experience[]> {
  try {
    const collection = await getExperienceCollection();
    const experienceDocs = await collection.find({}).sort({ duration: -1 }).toArray(); // Example sort
    return experienceDocs.map(doc => fromMongoDocument(doc as any) as Experience);
  } catch (error) {
    console.error('Error fetching experiences from MongoDB:', error);
    return [];
  }
}

const ExperienceSchema = z.object({
  companyName: z.string().min(1, 'Company name is required.'),
  role: z.string().min(1, 'Role is required.'),
  duration: z.string().min(1, 'Duration is required.'),
  description: z.array(z.string().min(1)).min(1, 'At least one description point is required.'),
});

export async function addExperience(
  formData: FormData
): Promise<{ success: boolean; message: string; experienceItem?: Experience; errors?: z.ZodIssue[] }> {
  const rawFormData = {
    companyName: formData.get('companyName'),
    role: formData.get('role'),
    duration: formData.get('duration'),
    description: formData.get('description') ? (formData.get('description') as string).split('\n').map(d => d.trim()).filter(d => d) : [],
  };

  const validationResult = ExperienceSchema.safeParse(rawFormData);

  if (!validationResult.success) {
    return { success: false, message: 'Validation failed.', errors: validationResult.error.issues };
  }

  const validatedData = validationResult.data;

  try {
    const collection = await getExperienceCollection();
    const result = await collection.insertOne(validatedData as Omit<Experience, 'id'>);
    
    if (!result.insertedId) {
        throw new Error('MongoDB insertion failed');
    }
    const newExperienceItem = fromMongoDocument({ _id: result.insertedId, ...validatedData } as any) as Experience;

    revalidatePath('/experience');
    revalidatePath('/admin/experience');
    revalidatePath('/');

    return { success: true, message: 'Experience added successfully.', experienceItem: newExperienceItem };
  } catch (error) {
    console.error('Error adding experience to MongoDB:', error);
    return { success: false, message: 'Failed to add experience.' };
  }
}

export async function updateExperience(
  id: string,
  formData: FormData
): Promise<{ success: boolean; message: string; experienceItem?: Experience; errors?: z.ZodIssue[] }> {
  if (!ObjectId.isValid(id)) {
    return { success: false, message: 'Invalid experience ID format.' };
  }
    const rawFormData = {
    companyName: formData.get('companyName'),
    role: formData.get('role'),
    duration: formData.get('duration'),
    description: formData.get('description') ? (formData.get('description') as string).split('\n').map(d => d.trim()).filter(d => d) : [],
  };

  const validationResult = ExperienceSchema.safeParse(rawFormData);
  if (!validationResult.success) {
    return { success: false, message: 'Validation failed.', errors: validationResult.error.issues };
  }

  const validatedData = validationResult.data;

  try {
    const collection = await getExperienceCollection();
    const result = await collection.updateOne(
      { _id: new ObjectId(id) },
      { $set: validatedData }
    );

    if (result.matchedCount === 0) {
      return { success: false, message: 'Experience item not found.' };
    }
    if (result.modifiedCount === 0 && result.matchedCount === 1) {
      console.log("Experience data was the same, no modification needed.");
    }
    
    const updatedExperienceItem = fromMongoDocument({ _id: new ObjectId(id), ...validatedData } as any) as Experience;

    revalidatePath('/experience');
    revalidatePath('/admin/experience');
    revalidatePath('/');
    return { success: true, message: 'Experience updated successfully.', experienceItem: updatedExperienceItem };
  } catch (error) {
    console.error(`Error updating experience ${id} in MongoDB:`, error);
    return { success: false, message: 'Failed to update experience.' };
  }
}

export async function deleteExperience(id: string): Promise<{ success: boolean; message: string }> {
  if (!ObjectId.isValid(id)) {
    return { success: false, message: 'Invalid experience ID format.' };
  }
  try {
    const collection = await getExperienceCollection();
    const result = await collection.deleteOne({ _id: new ObjectId(id) });

    if (result.deletedCount === 0) {
      return { success: false, message: 'Experience item not found.' };
    }

    revalidatePath('/experience');
    revalidatePath('/admin/experience');
    revalidatePath('/');
    return { success: true, message: 'Experience deleted successfully.' };
  } catch (error) {
    console.error(`Error deleting experience ${id} from MongoDB:`, error);
    return { success: false, message: 'Failed to delete experience.' };
  }
}
