
'use server';

import { z } from 'zod';
import { revalidatePath } from 'next/cache';
import type { Project } from '@/types';
import { getMongoDb, fromMongoDocument } from '@/lib/mongodb';
import { ObjectId, type Collection } from 'mongodb';
import { promises as fs } from 'fs';
import path from 'path';

const PROJECTS_COLLECTION = 'projects';
const UPLOAD_DIR = path.join(process.cwd(), 'public', 'uploads', 'projects');

async function getProjectsCollection(): Promise<Collection<Omit<Project, 'id'>>> {
  const db = await getMongoDb();
  return db.collection<Omit<Project, 'id'>>(PROJECTS_COLLECTION);
}

// Ensure upload directory exists
async function ensureUploadDirExists() {
  try {
    await fs.access(UPLOAD_DIR);
  } catch (error) {
    // Directory does not exist, create it
    await fs.mkdir(UPLOAD_DIR, { recursive: true });
  }
}

export async function getProjects(): Promise<Project[]> {
  try {
    const collection = await getProjectsCollection();
    const projectsDocs = await collection.find({}).toArray();
    return projectsDocs.map(doc => fromMongoDocument(doc as any) as Project);
  } catch (error) {
    console.error('Error fetching projects from MongoDB:', error);
    return [];
  }
}

export async function getProjectById(id: string): Promise<Project | undefined> {
  if (!ObjectId.isValid(id)) {
    console.warn('Invalid ObjectId format for getProjectById:', id);
    return undefined;
  }
  try {
    const collection = await getProjectsCollection();
    const projectDoc = await collection.findOne({ _id: new ObjectId(id) });
    return projectDoc ? fromMongoDocument(projectDoc as any) as Project : undefined;
  } catch (error) {
    console.error(`Error fetching project by ID ${id} from MongoDB:`, error);
    return undefined;
  }
}

const ProjectSchema = z.object({
  title: z.string().min(3, 'Title must be at least 3 characters.'),
  shortDescription: z.string().min(10, 'Short description must be at least 10 characters.'),
  technologies: z.array(z.string().min(1)).min(1, 'At least one technology is required.'),
  imageUrl: z.string().min(1, 'Image is required.'), // Will store path to uploaded image
  liveDemoUrl: z.string().url().optional().or(z.literal('')),
  githubRepoUrl: z.string().url().optional().or(z.literal('')),
  fullDescription: z.string().optional(),
});

const MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB
const ACCEPTED_IMAGE_TYPES = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];


export async function addProject(
  formData: FormData
): Promise<{ success: boolean; message: string; project?: Project; errors?: z.ZodIssue[] | { imageFile?: string } }> {

  await ensureUploadDirExists();

  const imageFile = formData.get('imageFile') as File | null;
  let imageUrlPath = '';

  if (!imageFile || imageFile.size === 0) {
    return { success: false, message: 'Validation failed.', errors: { imageFile: 'Project image is required.' } };
  }
  if (imageFile.size > MAX_FILE_SIZE) {
    return { success: false, message: 'Validation failed.', errors: { imageFile: `Image size must be less than ${MAX_FILE_SIZE / (1024*1024)}MB.` } };
  }
  if (!ACCEPTED_IMAGE_TYPES.includes(imageFile.type)) {
     return { success: false, message: 'Validation failed.', errors: { imageFile: 'Invalid image format. Only JPEG, PNG, WebP allowed.' } };
  }

  try {
    const bytes = await imageFile.arrayBuffer();
    const buffer = new Uint8Array(bytes);
    const filename = `${Date.now()}-${imageFile.name.replace(/\s+/g, '_')}`;
    const filepath = path.join(UPLOAD_DIR, filename);
    await fs.writeFile(filepath, buffer);
    imageUrlPath = `/uploads/projects/${filename}`;
  } catch (error) {
    console.error('Error saving image:', error);
    return { success: false, message: 'Failed to save project image.' };
  }

  const rawData = {
    title: formData.get('title'),
    shortDescription: formData.get('shortDescription'),
    technologies: formData.get('technologies') ? (formData.get('technologies') as string).split(',').map(t => t.trim()).filter(t => t) : [],
    imageUrl: imageUrlPath, // Use the path of the uploaded image
    liveDemoUrl: formData.get('liveDemoUrl') || undefined,
    githubRepoUrl: formData.get('githubRepoUrl') || undefined,
    fullDescription: formData.get('fullDescription') || undefined,
  };

  const validationResult = ProjectSchema.safeParse(rawData);

  if (!validationResult.success) {
    // If validation fails after image upload, attempt to delete uploaded image to prevent orphans
    if (imageUrlPath) {
      try { await fs.unlink(path.join(process.cwd(), 'public', imageUrlPath)); } catch (e) { console.error("Failed to delete orphaned image during validation error:", e); }
    }
    return { success: false, message: 'Validation failed.', errors: validationResult.error.issues };
  }

  const validatedData = {
      ...validationResult.data,
      liveDemoUrl: validationResult.data.liveDemoUrl === '' ? undefined : validationResult.data.liveDemoUrl,
      githubRepoUrl: validationResult.data.githubRepoUrl === '' ? undefined : validationResult.data.githubRepoUrl,
  };

  try {
    const collection = await getProjectsCollection();
    const result = await collection.insertOne(validatedData as Omit<Project, 'id'>);

    if (!result.insertedId) {
        throw new Error('MongoDB insertion failed');
    }

    const newProject = fromMongoDocument({ _id: result.insertedId, ...validatedData } as any) as Project;

    revalidatePath('/projects');
    revalidatePath('/admin/projects');
    revalidatePath('/');

    return { success: true, message: 'Project added successfully.', project: newProject };
  } catch (error) {
    console.error('Error adding project to MongoDB:', error);
    // Attempt to delete uploaded image if DB insert fails
    if (imageUrlPath) {
      try { await fs.unlink(path.join(process.cwd(), 'public', imageUrlPath)); } catch (e) { console.error("Failed to delete orphaned image after DB error:", e); }
    }
    return { success: false, message: 'Failed to add project.' };
  }
}

export async function updateProject(
  id: string,
  formData: FormData
): Promise<{ success: boolean; message: string; project?: Project; errors?: z.ZodIssue[] | { imageFile?: string } }> {
  if (!ObjectId.isValid(id)) {
    return { success: false, message: 'Invalid project ID format.' };
  }

  await ensureUploadDirExists();

  const imageFile = formData.get('imageFile') as File | null;
  let imageUrlPath = formData.get('currentImageUrl') as string || ''; // Keep current if no new file
  let oldImagePathToDelete: string | null = null;


  if (imageFile && imageFile.size > 0) {
    if (imageFile.size > MAX_FILE_SIZE) {
      return { success: false, message: 'Validation failed.', errors: { imageFile: `Image size must be less than ${MAX_FILE_SIZE / (1024*1024)}MB.` } };
    }
    if (!ACCEPTED_IMAGE_TYPES.includes(imageFile.type)) {
      return { success: false, message: 'Validation failed.', errors: { imageFile: 'Invalid image format. Only JPEG, PNG, WebP allowed.' } };
    }
    try {
      const bytes = await imageFile.arrayBuffer();
      const buffer = new Uint8Array(bytes);
      const filename = `${Date.now()}-${imageFile.name.replace(/\s+/g, '_')}`;
      const filepath = path.join(UPLOAD_DIR, filename);
      await fs.writeFile(filepath, buffer);

      // If there was an old image and it's different from the new one, mark it for deletion
      const newPath = `/uploads/projects/${filename}`;
      if (imageUrlPath && imageUrlPath !== newPath) {
        oldImagePathToDelete = imageUrlPath;
      }
      imageUrlPath = newPath;

    } catch (error) {
      console.error('Error saving new image during update:', error);
      return { success: false, message: 'Failed to save updated project image.' };
    }
  }

  const rawData = {
    title: formData.get('title'),
    shortDescription: formData.get('shortDescription'),
    technologies: formData.get('technologies') ? (formData.get('technologies') as string).split(',').map(t => t.trim()).filter(t => t) : [],
    imageUrl: imageUrlPath,
    liveDemoUrl: formData.get('liveDemoUrl') || undefined,
    githubRepoUrl: formData.get('githubRepoUrl') || undefined,
    fullDescription: formData.get('fullDescription') || undefined,
  };

  const validationResult = ProjectSchema.safeParse(rawData);

  if (!validationResult.success) {
    // If validation fails after a *new* image was uploaded, attempt to delete that new image
    if (imageFile && imageFile.size > 0 && imageUrlPath !== formData.get('currentImageUrl')) {
        try { await fs.unlink(path.join(process.cwd(), 'public', imageUrlPath)); } catch (e) { console.error("Failed to delete new orphaned image during update validation error:", e); }
    }
    return { success: false, message: 'Validation failed.', errors: validationResult.error.issues };
  }

  const validatedData = {
    ...validationResult.data,
    liveDemoUrl: validationResult.data.liveDemoUrl === '' ? undefined : validationResult.data.liveDemoUrl,
    githubRepoUrl: validationResult.data.githubRepoUrl === '' ? undefined : validationResult.data.githubRepoUrl,
  };

  try {
    const collection = await getProjectsCollection();
    const result = await collection.updateOne(
      { _id: new ObjectId(id) },
      { $set: validatedData }
    );

    if (result.matchedCount === 0) {
      // If project not found and new image was uploaded, delete it
      if (imageFile && imageFile.size > 0 && imageUrlPath !== formData.get('currentImageUrl')) {
        try { await fs.unlink(path.join(process.cwd(), 'public', imageUrlPath)); } catch (e) { console.error("Failed to delete new orphaned image, project not found:", e); }
      }
      return { success: false, message: 'Project not found.' };
    }
    if (result.modifiedCount === 0 && result.matchedCount === 1) {
      console.log("Project data was the same, no modification needed.");
    }

    // If update was successful and an old image was marked for deletion, delete it now
    if (oldImagePathToDelete) {
        try {
            await fs.unlink(path.join(process.cwd(), 'public', oldImagePathToDelete));
        } catch (e) {
            console.error("Failed to delete old project image after update:", e);
            // Non-critical, so don't fail the whole operation
        }
    }

    const updatedProject = fromMongoDocument({ _id: new ObjectId(id), ...validatedData } as any) as Project;

    revalidatePath('/projects');
    revalidatePath(`/projects/${id}`);
    revalidatePath('/admin/projects');
    revalidatePath('/');

    return { success: true, message: 'Project updated successfully.', project: updatedProject };
  } catch (error) {
    console.error(`Error updating project ${id} in MongoDB:`, error);
    // If DB update fails after a *new* image was uploaded, attempt to delete that new image
    if (imageFile && imageFile.size > 0 && imageUrlPath !== formData.get('currentImageUrl')) {
        try { await fs.unlink(path.join(process.cwd(), 'public', imageUrlPath)); } catch (e) { console.error("Failed to delete new orphaned image after DB update error:", e); }
    }
    return { success: false, message: 'Failed to update project.' };
  }
}

export async function deleteProject(id: string): Promise<{ success: boolean; message: string }> {
   if (!ObjectId.isValid(id)) {
    return { success: false, message: 'Invalid project ID format.' };
  }
  try {
    const collection = await getProjectsCollection();
    const projectToDelete = await collection.findOne({ _id: new ObjectId(id) });

    if (!projectToDelete) {
      return { success: false, message: 'Project not found.' };
    }

    const result = await collection.deleteOne({ _id: new ObjectId(id) });

    if (result.deletedCount === 0) {
      return { success: false, message: 'Project not found or already deleted.' };
    }

    // If project had an image, delete it from filesystem
    if (projectToDelete.imageUrl) {
      try {
        const imagePath = path.join(process.cwd(), 'public', projectToDelete.imageUrl);
        await fs.unlink(imagePath);
      } catch (error) {
        console.error(`Failed to delete project image ${projectToDelete.imageUrl}:`, error);
        // Log error but don't fail the whole deletion if image file deletion fails
      }
    }

    revalidatePath('/projects');
    revalidatePath('/admin/projects');
    revalidatePath('/');

    return { success: true, message: 'Project deleted successfully.' };
  } catch (error) {
    console.error(`Error deleting project ${id} from MongoDB:`, error);
    return { success: false, message: 'Failed to delete project.' };
  }
}

