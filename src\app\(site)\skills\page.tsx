
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { SkillCard } from '@/components/shared/SkillCard';
import type { Skill } from '@/types';
import { getSkills } from '@/actions/skillActions';
import { Lightbulb } from 'lucide-react';

// Define skill categories statically or fetch if they also become dynamic
const skillCategories: Skill['category'][] = ['Frontend', 'Backend', 'Tools', 'Design', 'Other'];

export default async function SkillsPage() {
  const skills = await getSkills();

  return (
    <div className="container mx-auto max-w-7xl px-4 py-6 sm:px-6 lg:px-8">
      <div className="text-center mb-16">
        <Lightbulb className="h-16 w-16 mx-auto text-primary mb-5" />
        <h1 className="text-4xl font-extrabold tracking-tight text-primary sm:text-5xl md:text-6xl">
          My Skills
        </h1>
        <p className="mt-6 max-w-3xl mx-auto text-xl text-muted-foreground">
          A collection of technologies and tools I'm proficient with.
        </p>
      </div>

      {skills.length > 0 ? (
        <Tabs defaultValue={skillCategories[0]} className="w-full">
          <TabsList className="grid w-full grid-cols-2 sm:grid-cols-3 md:grid-cols-5 mb-4">
            {skillCategories.map((category) => (
              <TabsTrigger key={category} value={category} className="text-sm sm:text-base">
                {category}
              </TabsTrigger>
            ))}
          </TabsList>
          {skillCategories.map((category) => (
            <TabsContent key={category} value={category}>
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                {skills
                  .filter((skill) => skill.category === category)
                  .map((skill) => (
                    <SkillCard key={skill.id} skill={skill} />
                  ))}
                {skills.filter((skill) => skill.category === category).length === 0 && (
                  <p className="col-span-full text-center text-muted-foreground py-8">No skills listed in this category yet.</p>
                )}
              </div>
            </TabsContent>
          ))}
        </Tabs>
      ) : (
        <div className="text-center py-12">
          <p className="text-xl text-muted-foreground">No skills to display yet. Check back soon!</p>
        </div>
      )}
    </div>
  );
}
