
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardDescription } from '@/components/ui/card';
import { Briefcase, CalendarDays } from 'lucide-react';
import type { Experience } from '@/types';

interface ExperienceCardProps {
  experience: Experience;
}

export function ExperienceCard({ experience }: ExperienceCardProps) {
  return (
    <Card className="shadow-lg hover:shadow-xl transition-shadow duration-300 ease-in-out w-full">
      <CardHeader>
        <div className="flex items-start justify-between gap-4">
          <div>
            <CardTitle className="text-xl font-semibold text-primary">{experience.role}</CardTitle>
            <CardDescription className="text-md text-foreground">{experience.companyName}</CardDescription>
          </div>
          <Briefcase className="h-8 w-8 text-muted-foreground flex-shrink-0 mt-1" />
        </div>
        <div className="flex items-center text-xs text-muted-foreground pt-1">
          <CalendarDays className="h-4 w-4 mr-1.5" />
          {experience.duration}
        </div>
      </CardHeader>
      <CardContent>
        <ul className="list-disc list-outside pl-5 space-y-2 text-sm text-muted-foreground">
          {experience.description.map((desc, index) => (
            <li key={index}>{desc}</li>
          ))}
        </ul>
      </CardContent>
    </Card>
  );
}
