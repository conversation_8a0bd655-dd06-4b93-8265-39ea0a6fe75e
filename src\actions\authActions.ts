
'use server';

import { z } from 'zod';

// For now, we won't implement session management with cookies.
// We'll just validate and let the client-side redirect.
// A more robust solution would involve setting an httpOnly cookie here
// and protecting admin routes/actions based on that session.

const LoginSchema = z.object({
  username: z.string().min(1, "Username is required."),
  password: z.string().min(1, "Password is required."),
});

export async function loginAdminAction(
  credentials: z.infer<typeof LoginSchema>
): Promise<{ success: boolean; message?: string }> {
  const parsedCredentials = LoginSchema.safeParse(credentials);

  if (!parsedCredentials.success) {
    // Construct a more specific error message from Zod errors if needed
    const errorMessages = parsedCredentials.error.errors.map(e => e.message).join(', ');
    return { success: false, message: `Invalid input: ${errorMessages}` };
  }

  const { username, password } = parsedCredentials.data;

  const adminUsername = process.env.ADMIN_USERNAME;
  const adminPassword = process.env.ADMIN_PASSWORD;

  if (!adminUsername || !adminPassword) {
    console.error("ADMIN_USERNAME or ADMIN_PASSWORD not set in .env file. Please ensure these are configured.");
    // Avoid revealing specific server configuration issues to the client in a production app.
    return { success: false, message: "Authentication service is currently unavailable. Please try again later." };
  }

  if (username === adminUsername && password === adminPassword) {
    // In a real app, here you would typically:
    // 1. Generate a session token (e.g., JWT).
    // 2. Store session information if needed (e.g., in a database or a secure cache for more complex scenarios).
    // 3. Set an httpOnly cookie with the session token to manage the session.
    // For this portfolio's current scope, we're just returning success.
    return { success: true };
  }

  return { success: false, message: "Invalid username or password." };
}
