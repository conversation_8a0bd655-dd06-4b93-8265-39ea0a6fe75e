
import { MongoClient, type Db, type ObjectId as MongoObjectId } from 'mongodb';

const MONGODB_URI = process.env.MONGODB_URI;
const MONGODB_DB_NAME = process.env.MONGODB_DB_NAME || 'profolio_db';

if (!MONGODB_URI) {
  throw new Error('Please define the MONGODB_URI environment variable inside .env');
}

// TypeScript assertion after runtime check
const MONGODB_URI_CHECKED: string = MONGODB_URI;

interface MongoCache {
  client: MongoClient | null;
  db: Db | null;
}

declare global {
  // eslint-disable-next-line no-var
  var mongo: MongoCache | undefined;
}

let cachedClient: MongoClient | null = null;
let cachedDb: Db | null = null;

if (process.env.NODE_ENV === 'development') {
  if (!global.mongo) {
    global.mongo = { client: null, db: null };
  }
  cachedClient = global.mongo.client;
  cachedDb = global.mongo.db;
}

export async function getMongoClient(): Promise<MongoClient> {
  if (cachedClient) {
    try {
      // Ping the database to ensure the connection is still alive
      await cachedClient.db('admin').command({ ping: 1 });
      // console.log("MongoDB: Reusing existing connected client."); // For debugging
      return cachedClient;
    } catch (e) {
      // console.warn("MongoDB: Cached client connection lost, attempting to reconnect.", e);
      // Connection is dead, clear caches and proceed to create a new one
      cachedClient = null;
      cachedDb = null; // Also invalidate Db cache as it depends on the client
      if (process.env.NODE_ENV === 'development' && global.mongo) {
        global.mongo.client = null;
        global.mongo.db = null;
      }
    }
  }

  // console.log("MongoDB: No cached client or connection lost, creating new connection."); // For debugging
  const client = new MongoClient(MONGODB_URI_CHECKED);

  try {
    await client.connect();
    // console.log("MongoDB: New client connected successfully."); // For debugging
  } catch (connectError) {
    console.error("MongoDB: Failed to connect new client.", connectError);
    throw connectError; // Re-throw connection error
  }

  cachedClient = client;
  if (process.env.NODE_ENV === 'development' && global.mongo) {
    global.mongo.client = cachedClient;
    // Reset global.mongo.db as well, as it's tied to the client
    global.mongo.db = null;
    cachedDb = null;
  }
  return cachedClient;
}


export async function getMongoDb(): Promise<Db> {
  if (cachedDb && cachedClient) {
    // If we have a cachedDb, ensure its client (cachedClient) is still alive.
    // getMongoClient will ping and return a live client or throw.
    try {
      const liveClient = await getMongoClient(); // This pings or reconnects
      if (liveClient === cachedClient) { // Check if the client instance is still the same
        // console.log("MongoDB: Reusing existing cachedDb as client is alive and unchanged.");
        return cachedDb;
      } else {
        // The cachedClient was deemed dead by getMongoClient and a new one was created.
        // So, the old cachedDb is invalid.
        // console.log("MongoDB: Cached client changed, invalidating cachedDb.");
        cachedDb = null;
         if (process.env.NODE_ENV === 'development' && global.mongo) {
            global.mongo.db = null;
        }
      }
    } catch (e) {
        // console.warn("MongoDB: Error verifying client for cachedDb. Clearing cachedDb.", e);
        cachedDb = null;
        if (process.env.NODE_ENV === 'development' && global.mongo) {
            global.mongo.db = null;
        }
        // Proceed to get a new client and db, error will be handled by getMongoClient if it persists
    }
  }

  const client = await getMongoClient(); // Ensures we have a live, connected client.
  const db = client.db(MONGODB_DB_NAME);

  cachedDb = db;
  if (process.env.NODE_ENV === 'development' && global.mongo) {
    global.mongo.db = cachedDb;
    // Ensure global.mongo.client is also up-to-date with the client that formed this db
    if (global.mongo.client !== client) {
        global.mongo.client = client;
    }
  }
  // console.log("MongoDB: Returning newly formed or re-validated db instance.");
  return db;
}

// Helper to convert MongoDB _id to string id
export function fromMongoDocument<T extends { _id: MongoObjectId | any }>(doc: T): Omit<T, '_id'> & { id: string } {
  const { _id, ...rest } = doc;
  return { ...rest, id: _id.toHexString() };
}

// This function might not be strictly necessary if you always query by ObjectId
export function toMongoDocument<T extends { id?: string }>(doc: T): Omit<T, 'id'> & { _id?: MongoObjectId } {
  if (doc.id && typeof doc.id === 'string') {
    // This case is more for when you might be inserting a document that already has a string ID
    // from another source, and you want to convert it to _id if it's a valid ObjectId hex string.
    // However, for querying, you usually convert the string ID to new ObjectId(id) in the query itself.
    // For inserts, MongoDB generates _id if not provided.
    // For updates, you query by _id: new ObjectId(stringId).
    // So, this function's utility depends on specific use cases.
    // For now, let's assume if 'id' is present, it's for client-side use and shouldn't become an _id field directly.
    const { id, ...rest } = doc;
    return rest;
  }
  return doc;
}
