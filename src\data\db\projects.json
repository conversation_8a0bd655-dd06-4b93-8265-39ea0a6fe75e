[{"id": "proj-1", "title": "Dynamic E-commerce Platform", "shortDescription": "A full-featured online store built with Next.js, fetching products dynamically.", "technologies": ["Next.js", "TypeScript", "Tailwind CSS", "<PERSON><PERSON>"], "imageUrl": "https://placehold.co/600x400.png?text=Dynamic+E-com", "liveDemoUrl": "#", "githubRepoUrl": "#", "fullDescription": "<p>This e-commerce platform showcases dynamic data loading and a robust backend (simulated). Key features include user authentication, product listings, and a shopping cart.</p><ul class=\"list-disc list-inside my-4 space-y-2\"><li>SSR and ISR for performance.</li><li>Admin panel for product management.</li><li>Secure (simulated) checkout.</li></ul>", "dataAiHint": "ecommerce store"}, {"id": "proj-2", "title": "Interactive Task Manager", "shortDescription": "A collaborative tool to organize tasks, track progress, powered by a dynamic backend.", "technologies": ["React", "Server Actions", "ShadCN UI"], "imageUrl": "https://placehold.co/600x400.png?text=Task+Manager+Pro", "liveDemoUrl": "#", "githubRepoUrl": "#", "fullDescription": "<p>This task management application allows users to create, update, and manage tasks in real-time. It features a clean UI and demonstrates efficient state management with server-side data persistence.</p>", "dataAiHint": "productivity app"}]