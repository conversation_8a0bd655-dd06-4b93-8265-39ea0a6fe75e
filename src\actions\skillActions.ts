
'use server';

import { z } from 'zod';
import { revalidatePath } from 'next/cache';
import type { Skill } from '@/types';
import { getMongoDb, fromMongoDocument } from '@/lib/mongodb';
import { ObjectId, type Collection } from 'mongodb';

const SKILLS_COLLECTION = 'skills';

async function getSkillsCollection(): Promise<Collection<Omit<Skill, 'id'>>> {
  const db = await getMongoDb();
  return db.collection<Omit<Skill, 'id'>>(SKILLS_COLLECTION);
}

export async function getSkills(): Promise<Skill[]> {
  try {
    const collection = await getSkillsCollection();
    const skillsDocs = await collection.find({}).toArray();
    return skillsDocs.map(doc => fromMongoDocument(doc as any) as Skill);
  } catch (error) {
    console.error('Error fetching skills from MongoDB:', error);
    return [];
  }
}

const SkillSchema = z.object({
  name: z.string().min(1, 'Skill name is required.'),
  category: z.enum(['Frontend', 'Backend', 'Tools', 'Design', 'Other']),
  proficiencyLevel: z.enum(['Beginner', 'Intermediate', 'Advanced', 'Expert']).optional(),
  // Note: iconName should be a valid Lucide icon name (keyof typeof import('lucide-react'))
  // We can't enumerate all possible values in the schema, so we validate as string
  // and rely on TypeScript casting when using the data
  iconName: z.string().optional(),
});

export async function addSkill(
  formData: FormData
): Promise<{ success: boolean; message: string; skill?: Skill; errors?: z.ZodIssue[] }> {
  const rawFormData = {
    name: formData.get('name'),
    category: formData.get('category'),
    proficiencyLevel: formData.get('proficiencyLevel') || undefined,
    iconName: formData.get('iconName') ? (formData.get('iconName') as string).split('-').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join('') : undefined,
  };

  const validationResult = SkillSchema.safeParse(rawFormData);

  if (!validationResult.success) {
    return { success: false, message: 'Validation failed.', errors: validationResult.error.issues };
  }

  const validatedData = validationResult.data;

  try {
    const collection = await getSkillsCollection();
    // Cast to Omit<Skill, "id"> to ensure type compatibility
    const result = await collection.insertOne(validatedData as unknown as Omit<Skill, "id">);

    if (!result.insertedId) {
        throw new Error('MongoDB insertion failed');
    }
    const newSkill = fromMongoDocument({ _id: result.insertedId, ...validatedData } as any) as Skill;

    revalidatePath('/skills');
    revalidatePath('/admin/skills');
    revalidatePath('/');

    return { success: true, message: 'Skill added successfully.', skill: newSkill };
  } catch (error) {
    console.error('Error adding skill to MongoDB:', error);
    return { success: false, message: 'Failed to add skill.' };
  }
}

export async function updateSkill(
  id: string,
  formData: FormData
): Promise<{ success: boolean; message: string; skill?: Skill; errors?: z.ZodIssue[] }> {
  if (!ObjectId.isValid(id)) {
    return { success: false, message: 'Invalid skill ID format.' };
  }
  
  const rawFormData = {
    name: formData.get('name'),
    category: formData.get('category'),
    proficiencyLevel: formData.get('proficiencyLevel') || undefined,
    iconName: formData.get('iconName') ? (formData.get('iconName') as string).split('-').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join('') : undefined,
  };

  const validationResult = SkillSchema.safeParse(rawFormData);
  if (!validationResult.success) {
    return { success: false, message: 'Validation failed.', errors: validationResult.error.issues };
  }

  const validatedData = validationResult.data;

  try {
    const collection = await getSkillsCollection();
    const result = await collection.updateOne(
      { _id: new ObjectId(id) },
      { $set: validatedData as unknown as Omit<Skill, "id"> }
    );

    if (result.matchedCount === 0) {
      return { success: false, message: 'Skill not found.' };
    }
     if (result.modifiedCount === 0 && result.matchedCount === 1) {
      console.log("Skill data was the same, no modification needed.");
    }

    const updatedSkill = fromMongoDocument({ _id: new ObjectId(id), ...validatedData } as any) as Skill;

    revalidatePath('/skills');
    revalidatePath('/admin/skills');
    revalidatePath('/');
    return { success: true, message: 'Skill updated successfully.', skill: updatedSkill };
  } catch (error) {
    console.error(`Error updating skill ${id} in MongoDB:`, error);
    return { success: false, message: 'Failed to update skill.' };
  }
}

export async function deleteSkill(id: string): Promise<{ success: boolean; message: string }> {
  if (!ObjectId.isValid(id)) {
    return { success: false, message: 'Invalid skill ID format.' };
  }
  try {
    const collection = await getSkillsCollection();
    const result = await collection.deleteOne({ _id: new ObjectId(id) });

    if (result.deletedCount === 0) {
      return { success: false, message: 'Skill not found.' };
    }

    revalidatePath('/skills');
    revalidatePath('/admin/skills');
    revalidatePath('/');
    return { success: true, message: 'Skill deleted successfully.' };
  } catch (error) {
    console.error(`Error deleting skill ${id} from MongoDB:`, error);
    return { success: false, message: 'Failed to delete skill.' };
  }
}
