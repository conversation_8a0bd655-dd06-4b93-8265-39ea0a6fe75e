
"use client";
import { useState, type FormEvent } from 'react';
import { useRouter } from 'next/navigation';
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { useToast } from "@/hooks/use-toast";
import { addProject } from '@/actions/projectActions';
import Link from 'next/link';
import { ArrowLeft, Save } from 'lucide-react'; // Removed ImageIcon as it's not directly used
import type { ZodIssue } from 'zod';

export default function NewProjectPage() {
  const router = useRouter();
  const { toast } = useToast();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errors, setErrors] = useState<Record<string, string | undefined>>({});
  const [imagePreview, setImagePreview] = useState<string | null>(null);

  const handleImageChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onloadend = () => {
        setImagePreview(reader.result as string);
      };
      reader.readAsDataURL(file);
      setErrors(prev => ({ ...prev, imageFile: undefined })); // Clear previous image error
    } else {
      setImagePreview(null);
    }
  };

  const handleSubmit = async (event: FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    setIsSubmitting(true);
    setErrors({});

    const formData = new FormData(event.currentTarget);
    const result = await addProject(formData);

    if (result.success) {
      toast({
        title: "Project Added",
        description: result.message,
      });
      router.push('/admin/projects');
    } else {
      toast({
        title: "Error Adding Project",
        description: result.message || "An unknown error occurred.",
        variant: "destructive",
      });
      if (result.errors) {
        const newErrors: Record<string, string> = {};
        if (Array.isArray(result.errors)) { // ZodIssue[]
            result.errors.forEach((err: ZodIssue) => {
            if (err.path[0]) {
                newErrors[err.path[0] as string] = err.message;
            }
            });
        } else { // Custom error like { imageFile: 'message' }
            Object.assign(newErrors, result.errors);
        }
        setErrors(newErrors);
      }
    }
    setIsSubmitting(false);
  };

  return (
    <div className="space-y-6">
       <Button variant="outline" size="sm" asChild className="mb-4">
        <Link href="/admin/projects"><ArrowLeft className="mr-2 h-4 w-4" />Back to Projects</Link>
      </Button>
      <Card>
        <CardHeader>
          <CardTitle className="text-2xl font-bold">Add New Project</CardTitle>
          <CardDescription>Fill in the details for the new project. Ensure all required fields are completed.</CardDescription>
        </CardHeader>
        <form onSubmit={handleSubmit}>
          <CardContent className="space-y-6"> {/* Increased spacing */}
            <div>
              <Label htmlFor="title" className="font-medium">Title</Label>
              <Input id="title" name="title" required disabled={isSubmitting} className="mt-1"/>
              {errors.title && <p className="text-sm text-destructive mt-1">{errors.title}</p>}
            </div>
            <div>
              <Label htmlFor="shortDescription" className="font-medium">Short Description</Label>
              <Textarea id="shortDescription" name="shortDescription" required disabled={isSubmitting} className="mt-1"/>
              {errors.shortDescription && <p className="text-sm text-destructive mt-1">{errors.shortDescription}</p>}
            </div>
            <div>
              <Label htmlFor="technologies" className="font-medium">Technologies (comma-separated)</Label>
              <Input id="technologies" name="technologies" placeholder="Next.js, TypeScript, Tailwind CSS" required disabled={isSubmitting} className="mt-1"/>
              {errors.technologies && <p className="text-sm text-destructive mt-1">{errors.technologies}</p>}
            </div>
            <div>
              <Label htmlFor="imageFile" className="font-medium">Project Image</Label>
              <Input 
                id="imageFile" 
                name="imageFile" 
                type="file" 
                accept="image/png, image/jpeg, image/webp" 
                required 
                disabled={isSubmitting}
                onChange={handleImageChange}
                className="mt-1 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-primary/10 file:text-primary hover:file:bg-primary/20"
              />
              {imagePreview && (
                <div className="mt-3">
                  <p className="text-xs text-muted-foreground mb-1">Image preview:</p>
                  {/* eslint-disable-next-line @next/next/no-img-element */}
                  <img src={imagePreview} alt="Selected image preview" className="h-32 w-auto rounded-md object-cover border" />
                </div>
              )}
              <p className="text-xs text-muted-foreground mt-1">Max 5MB. Accepted formats: JPEG, PNG, WebP.</p>
              {errors.imageFile && <p className="text-sm text-destructive mt-1">{errors.imageFile}</p>}
            </div>
            <div>
              <Label htmlFor="liveDemoUrl" className="font-medium">Live Demo URL (Optional)</Label>
              <Input id="liveDemoUrl" name="liveDemoUrl" type="url" disabled={isSubmitting} className="mt-1"/>
              {errors.liveDemoUrl && <p className="text-sm text-destructive mt-1">{errors.liveDemoUrl}</p>}
            </div>
            <div>
              <Label htmlFor="githubRepoUrl" className="font-medium">GitHub Repository URL (Optional)</Label>
              <Input id="githubRepoUrl" name="githubRepoUrl" type="url" disabled={isSubmitting} className="mt-1"/>
              {errors.githubRepoUrl && <p className="text-sm text-destructive mt-1">{errors.githubRepoUrl}</p>}
            </div>
            <div>
              <Label htmlFor="fullDescription" className="font-medium">Full Description (HTML allowed)</Label>
              <Textarea 
                id="fullDescription" 
                name="fullDescription" 
                rows={10} 
                placeholder="<p>Detailed description of your project...</p><ul><li>Feature 1</li><li>Feature 2</li></ul>" 
                disabled={isSubmitting}
                className="mt-1 min-h-[200px]"
              />
              <p className="text-xs text-muted-foreground mt-1">You can use HTML tags like &lt;p&gt;, &lt;ul&gt;, &lt;li&gt;, &lt;strong&gt; for formatting.</p>
              {errors.fullDescription && <p className="text-sm text-destructive mt-1">{errors.fullDescription}</p>}
            </div>
          </CardContent>
          <CardFooter>
            <Button type="submit" disabled={isSubmitting}>
              <Save className="mr-2 h-4 w-4" />
              {isSubmitting ? 'Adding Project...' : 'Add Project'}
            </Button>
          </CardFooter>
        </form>
      </Card>
    </div>
  );
}
