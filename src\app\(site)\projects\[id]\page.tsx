
import Image from 'next/image';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ArrowLeft, ExternalLink } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { getProjectById } from '@/actions/projectActions';
import { notFound } from 'next/navigation';

export default async function ProjectDetailPage({ params: paramsPromise }: { params: Promise<{ id: string }> }) {
  const params = await paramsPromise;
  const project = await getProjectById(params.id);

  if (!project) {
    notFound(); // Triggers the not-found page
  }

  // Ensure imageUrl is a valid path starting with / if it's from public folder
  const imageUrl = project.imageUrl.startsWith('/') ? project.imageUrl : `/${project.imageUrl}`;

  return (
    <div className="container mx-auto max-w-4xl px-4 py-16 sm:px-6 lg:px-8">
      <Button variant="outline" asChild className="mb-8">
        <Link href="/projects">
          <ArrowLeft className="mr-2 h-4 w-4" /> Back to Projects
        </Link>
      </Button>

      <Card className="shadow-xl overflow-hidden">
        <CardHeader className="p-0">
           <div className="relative aspect-[16/10]">
            <Image
                src={imageUrl} // Using the path directly
                alt={`Image of ${project.title}`}
                fill
                style={{ objectFit: "cover" }}
                data-ai-hint="project details interface" // Will be replaced by actual image content
            />
           </div>
        </CardHeader>
        <CardContent className="p-6 md:p-8">
          <CardTitle className="text-3xl md:text-4xl font-bold text-primary mb-2">{project.title}</CardTitle>
          <CardDescription className="text-lg text-muted-foreground mb-6">{project.shortDescription}</CardDescription>

          <div className="mb-6">
            <h3 className="text-xl font-semibold text-foreground mb-3">Technologies Used</h3>
            <div className="flex flex-wrap gap-2">
              {project.technologies.map((tech) => (
                <Badge key={tech} variant="secondary" className="text-sm px-3 py-1">{tech}</Badge>
              ))}
            </div>
          </div>

          {project.fullDescription && (
            <div className="mb-8">
              <h3 className="text-xl font-semibold text-foreground mb-3">Project Overview</h3>
              <div
                className="prose prose-sm sm:prose-base max-w-none text-muted-foreground space-y-4 dark:prose-invert prose-headings:text-foreground prose-a:text-primary hover:prose-a:text-primary/80 prose-strong:text-foreground"
                dangerouslySetInnerHTML={{ __html: project.fullDescription }}
              />
            </div>
          )}

          <div className="flex flex-wrap gap-4">
            {project.liveDemoUrl && (
              <Button asChild size="lg" className="flex-grow sm:flex-grow-0">
                <Link href={project.liveDemoUrl} target="_blank" rel="noopener noreferrer">
                  <ExternalLink className="mr-2 h-5 w-5" /> Live Demo
                </Link>
              </Button>
            )}
            {project.githubRepoUrl && (
              <Button variant="secondary" size="lg" asChild className="flex-grow sm:flex-grow-0">
                <Link href={project.githubRepoUrl} target="_blank" rel="noopener noreferrer">
                  <ExternalLink className="mr-2 h-5 w-5" /> View on GitHub
                </Link>
              </Button>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

