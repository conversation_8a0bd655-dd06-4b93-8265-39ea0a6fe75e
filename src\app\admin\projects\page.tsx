
"use client";
import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>ooter } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import Link from "next/link";
import { PlusCircle, Edit, Trash2 } from "lucide-react";
import { getProjects, deleteProject } from "@/actions/projectActions";
import type { Project } from "@/types";
import { useEffect, useState, useTransition } from "react";
import NextImage from "next/image";
import { useToast } from "@/hooks/use-toast";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";

export default function AdminProjectsPage() {
  const [projects, setProjects] = useState<Project[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isPending, startTransition] = useTransition();
  const { toast } = useToast();

  const fetchProjects = async () => {
    setIsLoading(true);
    const fetchedProjects = await getProjects();
    setProjects(fetchedProjects);
    setIsLoading(false);
  };

  useEffect(() => {
    fetchProjects();
  }, []);

  const handleDelete = async (id: string) => {
    startTransition(async () => {
      const result = await deleteProject(id);
      if (result.success) {
        toast({ title: "Success", description: result.message });
        fetchProjects(); 
      } else {
        toast({ title: "Error", description: result.message, variant: "destructive" });
      }
    });
  };

  if (isLoading) {
    return <p>Loading projects...</p>;
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <div>
            <CardTitle className="text-2xl font-bold">Manage Projects</CardTitle>
            <CardDescription>Create, read, update, and delete portfolio projects.</CardDescription>
          </div>
          <Button asChild>
            <Link href="/admin/projects/new">
              <PlusCircle className="mr-2 h-4 w-4" /> Add New Project
            </Link>
          </Button>
        </CardHeader>
        <CardContent>
          {projects.length > 0 ? (
            <div className="space-y-4">
              {projects.map(project => {
                // Ensure imageUrl is a valid path starting with / if it's from public folder
                // Or it could be an external URL if that's still supported by your logic elsewhere
                const imageUrl = project.imageUrl.startsWith('/') || project.imageUrl.startsWith('http') 
                                 ? project.imageUrl 
                                 : `/${project.imageUrl}`;
                return (
                <Card key={project.id} className="overflow-hidden">
                  <div className="flex flex-col sm:flex-row">
                    <div className="sm:w-1/3 md:w-1/4 relative aspect-video sm:aspect-square bg-muted overflow-hidden">
                      {project.imageUrl ? (
                        <NextImage 
                          src={imageUrl}
                          alt={project.title} 
                          fill 
                          style={{ objectFit: "cover" }}
                          // If using external URLs not in next.config.js, unoptimized might be needed
                          // For local public paths, this is usually not required.
                          unoptimized={imageUrl.startsWith('http') && !imageUrl.includes('placehold.co')}
                        />
                      ) : (
                        <div className="w-full h-full flex items-center justify-center text-muted-foreground text-sm p-4 text-center">No Image Available</div>
                      )}
                    </div>
                    <div className="flex-1">
                      <CardHeader>
                        <CardTitle>{project.title}</CardTitle>
                        <CardDescription className="line-clamp-2">{project.shortDescription}</CardDescription>
                      </CardHeader>
                      <CardContent className="text-sm">
                        <p><strong>Technologies:</strong> {project.technologies.join(', ')}</p>
                        {project.liveDemoUrl && <p className="truncate"><strong>Demo:</strong> <a href={project.liveDemoUrl} target="_blank" rel="noopener noreferrer" className="text-primary hover:underline">{project.liveDemoUrl}</a></p>}
                        {project.githubRepoUrl && <p className="truncate"><strong>Repo:</strong> <a href={project.githubRepoUrl} target="_blank" rel="noopener noreferrer" className="text-primary hover:underline">{project.githubRepoUrl}</a></p>}
                      </CardContent>
                      <CardFooter className="flex justify-end gap-2">
                        <Button variant="outline" size="sm" asChild>
                          <Link href={`/admin/projects/edit/${project.id}`}>
                            <Edit className="mr-2 h-4 w-4" /> Edit
                          </Link>
                        </Button>
                        <AlertDialog>
                          <AlertDialogTrigger asChild>
                            <Button variant="destructive" size="sm" disabled={isPending}>
                              <Trash2 className="mr-2 h-4 w-4" /> Delete
                            </Button>
                          </AlertDialogTrigger>
                          <AlertDialogContent>
                            <AlertDialogHeader>
                              <AlertDialogTitle>Are you sure?</AlertDialogTitle>
                              <AlertDialogDescription>
                                This action cannot be undone. This will permanently delete the project "{project.title}" and its associated image.
                              </AlertDialogDescription>
                            </AlertDialogHeader>
                            <AlertDialogFooter>
                              <AlertDialogCancel>Cancel</AlertDialogCancel>
                              <AlertDialogAction onClick={() => handleDelete(project.id)} disabled={isPending}>
                                {isPending ? 'Deleting...' : 'Delete'}
                              </AlertDialogAction>
                            </AlertDialogFooter>
                          </AlertDialogContent>
                        </AlertDialog>
                      </CardFooter>
                    </div>
                  </div>
                </Card>
              );
              })}
            </div>
          ) : (
            <p className="text-muted-foreground text-center py-8">No projects added yet.</p>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
