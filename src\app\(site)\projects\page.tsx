
import { ProjectCard } from '@/components/shared/ProjectCard';
import { getProjects } from '@/actions/projectActions';
import { Briefcase } from 'lucide-react';

export default async function ProjectsPage() {
  const projects = await getProjects();

  return (
    <div className="container mx-auto max-w-7xl px-4 py-6 sm:px-6 lg:px-8">
      <div className="text-center mb-16">
        <Briefcase className="h-16 w-16 mx-auto text-primary mb-5" />
        <h1 className="text-4xl font-extrabold tracking-tight text-primary sm:text-5xl md:text-6xl">
          My Projects
        </h1>
      
      </div>
      
      {projects.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {projects.map((project) => (
            <ProjectCard key={project.id} project={project} />
          ))}
        </div>
      ) : (
        <div className="text-center py-12">
          <p className="text-xl text-muted-foreground">No projects to display yet. Check back soon!</p>
        </div>
      )}
    </div>
  );
}
