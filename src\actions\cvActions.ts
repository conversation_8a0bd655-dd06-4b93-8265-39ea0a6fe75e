
'use server';

import { promises as fs } from 'fs';
import path from 'path';
import { <PERSON><PERSON><PERSON> } from 'buffer';
import { revalidatePath } from 'next/cache';

export async function uploadCv(formData: FormData): Promise<{ success: boolean; message: string }> {
  const file = formData.get('cvFile') as File;

  if (!file) {
    return { success: false, message: 'No file uploaded.' };
  }

  if (file.type !== 'application/pdf') {
    return { success: false, message: 'Invalid file type. Only PDF is allowed.' };
  }

  if (file.size === 0) {
    return { success: false, message: 'File is empty.' };
  }

  // Max file size (e.g., 5MB)
  const maxSize = 5 * 1024 * 1024;
  if (file.size > maxSize) {
    return { success: false, message: `File is too large. Max size is ${maxSize / (1024*1024)}MB.` };
  }

  try {
    const bytes = await file.arrayBuffer();
    const buffer = new Uint8Array(bytes);

    const cvFileName = 'cv.pdf';
    const filePath = path.join(process.cwd(), 'public', cvFileName);

    await fs.writeFile(filePath, buffer);

    // Revalidate the home page if needed, though direct link to /cv.pdf might not require this.
    // For good measure, if the CV content could influence other parts of the site.
    revalidatePath('/');
    revalidatePath('/admin/cv-management');


    return { success: true, message: 'CV uploaded successfully.' };
  } catch (error) {
    console.error('Error uploading CV:', error);
    return { success: false, message: 'Failed to upload CV. Please try again.' };
  }
}

export async function getCurrentCvInfo(): Promise<{ fileName: string | null; lastModified: Date | null }> {
  const cvFileName = 'cv.pdf';
  const filePath = path.join(process.cwd(), 'public', cvFileName);
  try {
    const stats = await fs.stat(filePath);
    return { fileName: cvFileName, lastModified: stats.mtime };
  } catch (error) {
    // File doesn't exist or other error
    return { fileName: null, lastModified: null };
  }
}
