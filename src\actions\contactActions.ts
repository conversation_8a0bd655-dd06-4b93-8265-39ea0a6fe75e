
'use server';

import { z } from 'zod';
import { revalidatePath } from 'next/cache';
import type { ContactMessage } from '@/types';
import { getMongoDb, fromMongoDocument } from '@/lib/mongodb';
import { ObjectId, type Collection } from 'mongodb';

const CONTACT_MESSAGES_COLLECTION = 'contactMessages';

async function getContactMessagesCollection(): Promise<Collection<Omit<ContactMessage, 'id'>>> {
  const db = await getMongoDb();
  return db.collection<Omit<ContactMessage, 'id'>>(CONTACT_MESSAGES_COLLECTION);
}

export async function getContactMessages(): Promise<ContactMessage[]> {
  try {
    const collection = await getContactMessagesCollection();
    // Sort by submittedAt in descending order (newest first)
    const messagesDocs = await collection.find({}).sort({ submittedAt: -1 }).toArray();
    return messagesDocs.map(doc => fromMongoDocument(doc as any) as ContactMessage);
  } catch (error) {
    console.error('Error fetching contact messages from MongoDB:', error);
    return [];
  }
}

const ContactFormSchema = z.object({
  name: z.string().min(2, "Name must be at least 2 characters.").max(50, "Name must be at most 50 characters."),
  email: z.string().email("Invalid email address."),
  message: z.string().min(10, "Message must be at least 10 characters.").max(500, "Message must be at most 500 characters."),
});

export async function submitContactForm(
  prevState: { message: string; errors?: z.ZodIssue[]; success?: boolean } | null,
  formData: FormData
): Promise<{ message: string; errors?: z.ZodIssue[]; success?: boolean }> {
  const rawFormData = {
    name: formData.get('name'),
    email: formData.get('email'),
    message: formData.get('message'),
  };

  const validationResult = ContactFormSchema.safeParse(rawFormData);

  if (!validationResult.success) {
    return {
      message: 'Validation failed. Please check the errors below.',
      errors: validationResult.error.issues,
      success: false,
    };
  }

  const validatedData = validationResult.data;
  const newMessageData: Omit<ContactMessage, 'id'> = {
    ...validatedData,
    submittedAt: new Date().toISOString(),
  };

  try {
    const collection = await getContactMessagesCollection();
    await collection.insertOne(newMessageData);

    revalidatePath('/admin/messages');
    // In a real app, you'd also send an email notification here.
    return { message: 'Message sent successfully! Thank you for reaching out.', success: true };
  } catch (error) {
    console.error('Error submitting contact form to MongoDB:', error);
    return { message: 'Failed to send message due to a server error.', success: false };
  }
}

export async function deleteContactMessage(id: string): Promise<{ success: boolean; message: string }> {
  if (!ObjectId.isValid(id)) {
    return { success: false, message: 'Invalid message ID format.' };
  }
  try {
    const collection = await getContactMessagesCollection();
    const result = await collection.deleteOne({ _id: new ObjectId(id) });

    if (result.deletedCount === 0) {
      return { success: false, message: 'Message not found.' };
    }

    revalidatePath('/admin/messages');
    return { success: true, message: 'Message deleted successfully.' };
  } catch (error) {
    console.error(`Error deleting contact message ${id} from MongoDB:`, error);
    return { success: false, message: 'Failed to delete message.' };
  }
}
